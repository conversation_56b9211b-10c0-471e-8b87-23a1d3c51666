"use client";
import * as React from "react";
import { useEffect } from "react";
import Autoplay from "embla-carousel-autoplay";

// import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { useSelector } from "react-redux";
import store, { RootState } from "@/store/store";
import { fetchBanners } from "@/store/slices/banner-slice";
import { getImageUrl } from "@/utils/image-url";

export const BannerCarousel = () => {
  const { banners, loading, error } = useSelector(
    (state: RootState) => state.banner
  );

  // const banners = [
  //   "/banner1.webp",
  //   "/banner2.webp",
  //   "/banner3.webp",
  //   "/banner4.webp",
  //   "/banner5.webp",
  // ];
  const plugin = React.useRef(
    Autoplay({ delay: 2000, stopOnInteraction: true })
  );

  useEffect(() => {
    if (!banners.length) {
      store.dispatch(fetchBanners());
    }
    return () => {};
  }, []);

  return (
    <Carousel
      plugins={[plugin.current]}
      className="w-full h-max"
      onMouseEnter={plugin.current.stop}
      onMouseLeave={plugin.current.reset}
    >
      <CarouselContent className="w-full h-max">
        {banners.map((banner, index) => (
          <CarouselItem key={index} className="">
            <Image
              src={getImageUrl(banner.image)}
              alt="banner"
              width={1500}
              height={100}
              className="w-full h-auto"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
};
