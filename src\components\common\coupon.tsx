import store, { RootState } from "@/store/store";
import { formatPrice } from "@/utils/format-price";
import { Tag, X } from "lucide-react";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Button } from "../ui/button";
import { applyCoupon, removeCoupon } from "@/store/slices/cart-slice";
import { toast } from "sonner";
import { calculateTaxes, getTotal } from "@/utils/cart-price";
import { Input } from "../ui/input";

export const Coupon = () => {
  const {
    cart,
    tax: { taxes },
  } = useSelector((state: RootState) => state);
  const [loading, setLoading] = useState<boolean>(false);
  const [promoCode, setPromoCode] = useState<string>("");

  const onApplyCoupon = () => {
    // store.dispatch(applyCoupon(promoCode));

    if (!promoCode.trim()) return;

    setLoading(false);
    store
      .dispatch(applyCoupon(promoCode))
      .unwrap()
      .then((response) => {
        toast.success("Coupon applied successfully");
        setPromoCode("");
      })
      .catch((error) => {
        toast.error(error.message || "Invalid coupon code");
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="mt-4">
      {" "}
      {cart.coupon ? (
        <div className="mb-6 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm font-medium flex items-center">
                <Tag className="w-4 h-4 mr-1 text-green-600" />
                Coupon Applied:{" "}
                <span className="font-bold ml-1">{cart.coupon.code}</span>
              </p>
              <p className="text-xs text-green-600 mt-1">
                {cart.coupon.discountType === "percentage"
                  ? `${cart.coupon.discountValue}% off your order`
                  : `${formatPrice({
                      price: cart.coupon.discountValue,
                    })} off your order`}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500 hover:text-red-500 hover:bg-red-50"
              onClick={() => {
                store.dispatch(removeCoupon());
                toast.success("Coupon removed");
              }}
              title="Remove coupon"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          {cart.couponDiscount > 0 && (
            <div className="mt-3 pt-2 border-t border-green-200">
              <p className="text-sm flex justify-between items-center text-green-600">
                <span>Discount amount:</span>
                <span className="font-bold">
                  -{formatPrice({ price: cart.couponDiscount })}
                </span>
              </p>
              <p className="text-sm flex justify-between items-center mt-1">
                <span className="font-medium">New total:</span>
                <span className="font-bold">
                  {formatPrice({
                    price:
                      +calculateTaxes(taxes, cart.cart) +
                      getTotal(cart.cart, {
                        discount: cart.automatic_discount?.amount,
                        couponDiscount: cart.couponDiscount || 0,
                      }),
                  })}
                </span>
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className="mb-6 p-3 border border-dashed border-gray-300 rounded-md">
          <p className="text-sm font-medium mb-2 flex items-center">
            <Tag className="w-4 h-4 mr-1" />
            Have a promo code?
          </p>
          <div className="flex items-center">
            <Input
              containerClassName="w-full mr-2"
              className="rounded-md border-gray-300 text-sm"
              placeholder="Enter code"
              value={promoCode}
              onChange={(e) => setPromoCode(e.target.value)}
              disabled={loading}
            />
            <Button
              className="bg-primary hover:bg-primary/90 text-sm px-3"
              onClick={onApplyCoupon}
              disabled={!promoCode.trim() || loading}
            >
              {loading ? "Applying..." : "Apply"}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
