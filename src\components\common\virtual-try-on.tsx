"use client";

import React, { useRef, useEffect, useState } from "react";
import Webcam from "react-webcam";
import * as THREE from "three";
import * as tf from "@tensorflow/tfjs-core";
import "@tensorflow/tfjs-converter";
import "@tensorflow/tfjs-backend-webgl";
import {
  load,
  SupportedPackages,
  FaceLandmarksDetector,
} from "@tensorflow-models/face-landmarks-detection";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { useRouter } from "next/navigation";
import { api_interceptor } from "@/utils/api-interceptor";
import { PRODUCT_URL } from "@/constants/url";
import { getImageUrl } from "@/utils/image-url";

function isGLB(url: string): boolean {
  return (
    url.toLowerCase().endsWith(".glb") || url.toLowerCase().endsWith(".gltf")
  );
}

type Props = {
  slug: any;
};

export const VirtualTryOn: React.FC<Props> = ({ slug }) => {
  const webcamRef = useRef<Webcam>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [model, setModel] = useState<FaceLandmarksDetector | null>(null);
  const [glassesMesh, setGlassesMesh] = useState<THREE.Object3D | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [scene, setScene] = useState<THREE.Scene | null>(null);
  const [renderer, setRenderer] = useState<THREE.WebGLRenderer | null>(null);
  const [camera3D, setCamera3D] = useState<THREE.PerspectiveCamera | null>(
    null
  );
  const [alignmentMode, setAlignmentMode] = useState(true);
  const [faceCentered, setFaceCentered] = useState(false);
  const [showStartAnyway, setShowStartAnyway] = useState(false);

  const [frames, setFrames] = useState<string[]>([]);
  const [currentFrame, setCurrentFrame] = useState<string>("");
  const [stream, setStream] = useState<MediaStream | null>(null);
  const router = useRouter();

  useEffect(() => {
    const loadResources = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 800, facingMode: "user" },
        });
        setStream(mediaStream);

        if (webcamRef.current && webcamRef.current.video) {
          webcamRef.current.video.srcObject = mediaStream;
        }

        await tf.setBackend("webgl");
        await tf.ready();
        const loadedModel = await load(SupportedPackages.mediapipeFacemesh, {
          shouldLoadIrisModel: true,
          maxFaces: 1,
        });
        setModel(loadedModel);

        if (canvasRef.current) {
          const width = canvasRef.current.clientWidth;
          const height = canvasRef.current.clientHeight;
          const sceneNew = new THREE.Scene();
          const cameraNew = new THREE.PerspectiveCamera(
            75,
            width / height,
            0.1,
            1000
          );
          cameraNew.position.z = 5;
          const rendererNew = new THREE.WebGLRenderer({
            canvas: canvasRef.current,
            alpha: true,
          });
          rendererNew.setSize(width, height);
          rendererNew.setAnimationLoop(() =>
            rendererNew.render(sceneNew, cameraNew)
          );
          setScene(sceneNew);
          setCamera3D(cameraNew);
          setRenderer(rendererNew);
        }

        await fetchProduct(slug);
      } catch (error) {
        console.error("Initialization error:", error);
        setIsLoading(false);
      }
    };

    loadResources();

    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      if (renderer) {
        renderer.setAnimationLoop(null);
        renderer.dispose();
      }
    };
  }, []);

  const fetchProduct = async (slug: string) => {
    try {
      const res = await api_interceptor.get(PRODUCT_URL + `/${slug}`);
      // const thumbnail = res.data?.data?.thumbnail;
      const images = [
        ...res.data.data.images,
        "/sunglass/1.glb",
        "/sunglass/2.glb",
        "/sunglass/3.glb",
      ];
      setFrames(images);

      setCurrentFrame(isGLB(images[0]) ? images[0] : getImageUrl(images[0]));
      // if (thumbnail) {
      // }
    } catch (error) {
      console.error("Product fetch error:", error);
    }
  };

  const loadGlasses = (uri: string) => {
    if (!scene) return;
    if (glassesMesh) {
      scene.remove(glassesMesh);
    }
    if (isGLB(uri)) {
      const loader = new GLTFLoader();
      loader.load(uri, (gltf) => {
        const model = gltf.scene;
        model.scale.set(1, 1, 1);
        scene.add(model);
        setGlassesMesh(model);
      });
    } else {
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load(uri, (texture) => {
        texture.colorSpace = THREE.SRGBColorSpace;
        const geometry = new THREE.PlaneGeometry(2, 1);
        const material = new THREE.MeshBasicMaterial({
          map: texture,
          transparent: true,
        });
        const glasses = new THREE.Mesh(geometry, material);
        scene.add(glasses);
        setGlassesMesh(glasses);
      });
    }
  };

  useEffect(() => {
    if (currentFrame && scene) {
      loadGlasses(currentFrame);
    }
  }, [currentFrame, scene]);

  useEffect(() => {
    const detectAndPositionGlasses = async () => {
      if (
        !webcamRef.current?.video ||
        !model ||
        !glassesMesh ||
        webcamRef.current.video.readyState !== 4
      )
        return;

      const faceEstimates: any[] = await model.estimateFaces({
        input: webcamRef.current.video,
      });

      if (faceEstimates.length === 0) return;

      setIsLoading(false);

      const keypoints = faceEstimates[0].scaledMesh as number[][];

      const eyeCenter = keypoints[168];
      const leftEye = keypoints[130];
      const rightEye = keypoints[359];

      const videoWidth = webcamRef.current.video.videoWidth;
      const videoHeight = webcamRef.current.video.videoHeight;

      const xNorm = eyeCenter[0] / videoWidth;
      const yNorm = eyeCenter[1] / videoHeight;

      const distanceFromCenter = Math.sqrt(
        Math.pow(xNorm - 0.5, 2) + Math.pow(yNorm - 0.5, 2)
      );

      if (distanceFromCenter < 0.2) {
        if (!faceCentered) {
          setFaceCentered(true);
          setTimeout(() => setAlignmentMode(false), 500);
        }
      } else {
        setFaceCentered(false);
      }

      if (alignmentMode) return;

      // Distance between eyes
      const eyeDistance = Math.sqrt(
        Math.pow(rightEye[0] - leftEye[0], 2) +
          Math.pow(rightEye[1] - leftEye[1], 2)
      );

      const is3DModel = !(glassesMesh as any).isMesh; // true if it's a GLTF model
      const scale = is3DModel ? (eyeDistance / 100) * 0.9 : 1;

      const scaleX = -0.01;
      const scaleY = -0.01;
      const offsetX = 0.0;
      const offsetY = is3DModel ? -0.01 : -0.2;

      glassesMesh.position.x =
        (eyeCenter[0] - videoWidth / 2) * scaleX + offsetX;
      glassesMesh.position.y =
        (eyeCenter[1] - videoHeight / 2) * scaleY + offsetY;
      glassesMesh.position.z = is3DModel ? 0.3 : 1;
      glassesMesh.scale.set(scale, scale, scale);

      const eyeLine = new THREE.Vector2(
        rightEye[0] - leftEye[0],
        rightEye[1] - leftEye[1]
      );
      glassesMesh.rotation.set(0, 0, Math.atan2(eyeLine.y, eyeLine.x));

      // 💡 OPTIONAL: Resize 2D frame geometry dynamically
      if (
        !is3DModel &&
        (glassesMesh as any).geometry instanceof THREE.PlaneGeometry
      ) {
        const aspectRatio = 2.2;
        const width = eyeDistance / 75;
        const height = width / aspectRatio;
        (glassesMesh as THREE.Mesh).geometry.dispose();
        (glassesMesh as THREE.Mesh).geometry = new THREE.PlaneGeometry(
          width,
          height
        );
      }
    };

    const intervalId = setInterval(() => {
      detectAndPositionGlasses();
    }, 120);

    return () => clearInterval(intervalId);
  }, [model, glassesMesh, alignmentMode]);

  useEffect(() => {
    if (alignmentMode) {
      const timeout = setTimeout(() => {
        setShowStartAnyway(true);
      }, 10000);
      return () => clearTimeout(timeout);
    }
  }, [alignmentMode]);

  return (
    <>
      <div className="border-b border-black/20 text-center mb-4">
        <h1 className="text-lg font-semibold py-4">Virtual Try-On</h1>
      </div>

      <div className="w-full flex justify-between items-center px-4 py-2">
        <button
          className="px-4 py-2 bg-black text-white rounded-md"
          onClick={() => {
            if (stream) {
              stream.getTracks().forEach((track) => track.stop());
            }
            if (renderer) {
              renderer.setAnimationLoop(null);
              renderer.dispose();
            }
            router.push("/product");
          }}
        >
          ← Back to Products
        </button>
      </div>

      <div className="relative mx-auto w-[90vw] max-w-[400px] aspect-[3/4] rounded-md overflow-hidden shadow-lg bg-black/5">
        {isLoading && (
          <div className="absolute top-0 left-0 w-full h-full bg-white/50 flex justify-center items-center z-20">
            <h3>Loading...</h3>
          </div>
        )}

        {!isLoading && alignmentMode && (
          <div className="absolute inset-0 flex flex-col items-center justify-center z-30 bg-black/50">
            <div className="relative">
              <div
                className="w-60 h-60 rounded-full border-4 border-white animate-pulse-slow"
                style={{
                  background:
                    "radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.6) 42%)",
                }}
              ></div>
              <p className="text-white mt-6 text-center text-sm">
                Align your face inside the circle
              </p>
              {showStartAnyway && (
                <div className="flex justify-center">
                  <button
                    onClick={() => setAlignmentMode(false)}
                    className="mt-6 px-4 py-2 bg-white text-black rounded-md text-sm"
                  >
                    Start Anyway
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <Webcam
          ref={webcamRef}
          mirrored
          autoPlay
          playsInline
          videoConstraints={{
            width: 640,
            height: 800,
            facingMode: "user",
          }}
          className="absolute inset-0 object-cover w-full h-full"
        />

        <canvas
          ref={canvasRef}
          width={640}
          height={800}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />
      </div>

      {frames.length > 0 && (
        <div className="flex flex-wrap gap-4 overflow-x-auto mt-6 mb-6 px-2 justify-center">
          {frames.map((frame, idx) => (
            <button
              key={idx}
              className={`border-2 rounded-md overflow-hidden w-24 h-24 flex-shrink-0 ${
                currentFrame === getImageUrl(frame)
                  ? "border-blue-500"
                  : "border-gray-300"
              }`}
              onClick={() =>
                setCurrentFrame(isGLB(frame) ? frame : getImageUrl(frame))
              }
            >
              <img
                src={getImageUrl(frame)}
                alt={`Frame ${idx + 1}`}
                className="w-full h-full object-contain"
              />
            </button>
          ))}
        </div>
      )}
    </>
  );
};
