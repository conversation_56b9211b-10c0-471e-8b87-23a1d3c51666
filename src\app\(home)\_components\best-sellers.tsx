"use client";
import React, { useEffect, useState } from "react";
import { Product } from "@/components/common/product";
import { Button } from "@/components/ui/button";
import { IPaginator, IProduct } from "@/interfaces";
import { api_interceptor } from "@/utils/api-interceptor";
import { PRODUCT_URL } from "@/constants/url";

export const BestSellers = () => {
  const [products, setProducts] = useState<IProduct[]>([]);
  const [paginator, setPaginator] = useState<IPaginator>({
    page: 0,
    limit: 4,
    total: 0,
    totalPages: 1,
  });

  const loadMore = () => {
    api_interceptor
      .get(
        PRODUCT_URL +
          `?page=${paginator.page + 1}&limit=${paginator.limit}&sort=popularity`
      )
      .then((res) => {
        setProducts([...products, ...res.data.data.products]);
        setPaginator(res.data.data.paginator);
      });
  };

  useEffect(() => {
    loadMore();
    return () => {};
  }, []);

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-3xl font-semibold text-center font-workSans">
          Best Sellers
        </h1>
      </div>
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {products.map((product, index) => (
          <Product key={index} product={product} />
        ))}
      </div>
      {paginator.totalPages > paginator.page && (
        <div className="flex justify-center">
          <Button onClick={loadMore}>Load more</Button>
        </div>
      )}
    </div>
  );
};
